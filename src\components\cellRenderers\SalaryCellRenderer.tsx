import React from "react";
import type { ICellRendererParams } from "ag-grid-community";

/**
 * Custom cell renderer for salary display
 * Formats salary with proper currency formatting and visual indicators
 * Used in the AG Grid to show salary information in a professional format
 */
interface SalaryCellRendererProps extends ICellRendererParams {
	value: number;
}

export const SalaryCellRenderer: React.FC<SalaryCellRendererProps> = ({ value }) => {
	/**
	 * Formats salary as currency string
	 */
	const formatSalary = (salary: number): string => {
		return new Intl.NumberFormat("en-US", {
			style: "currency",
			currency: "USD",
			minimumFractionDigits: 0,
			maximumFractionDigits: 0,
		}).format(salary);
	};

	/**
	 * Returns salary range category for color coding
	 */
	const getSalaryRange = (salary: number): "entry" | "mid" | "senior" | "executive" => {
		if (salary >= 150000) return "executive";
		if (salary >= 100000) return "senior";
		if (salary >= 70000) return "mid";
		return "entry";
	};

	/**
	 * Returns appropriate styling based on salary range
	 */
	const getSalaryStyle = (range: string): string => {
		switch (range) {
			case "executive":
				return "text-purple-700 font-semibold";
			case "senior":
				return "text-blue-700 font-medium";
			case "mid":
				return "text-green-700 font-medium";
			case "entry":
				return "text-gray-700";
			default:
				return "text-gray-700";
		}
	};

	/**
	 * Returns salary range indicator
	 */
	const getSalaryIndicator = (range: string): string => {
		switch (range) {
			case "executive":
				return "💎";
			case "senior":
				return "🔷";
			case "mid":
				return "🔹";
			case "entry":
				return "⚪";
			default:
				return "";
		}
	};

	const salaryRange = getSalaryRange(value);
	const formattedSalary = formatSalary(value);
	const salaryStyle = getSalaryStyle(salaryRange);
	const indicator = getSalaryIndicator(salaryRange);

	return (
		<div className="flex items-center h-full space-x-2">
			{/* Salary indicator */}
			<span className="text-sm" title={`${salaryRange} level`}>
				{indicator}
			</span>

			{/* Formatted salary */}
			<span className={`text-sm ${salaryStyle}`}>{formattedSalary}</span>

			{/* Range label (hidden on small screens) */}
			<span className="text-xs text-gray-500 capitalize hidden xl:inline">({salaryRange})</span>
		</div>
	);
};
